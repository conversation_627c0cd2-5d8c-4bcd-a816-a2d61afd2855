% Test file for HTML forms functionality and inheritance structure

:- object(test_forms).

    :- public(test_basic_form/0).
    :- public(test_form_data/0).
    :- public(test_inheritance/0).
    :- public(test_generic_input_form/0).

    % Test creating a basic form
    test_basic_form :-
        jupyter_forms::create_input_form(
            test_form,
            [
                text_field(name, 'Name:', ''),
                email_field(email, 'Email:', ''),
                number_field(age, 'Age:', 25),
                dropdown_field(country, 'Country:', [usa, canada, uk, germany]),
                checkbox_field(newsletter, 'Subscribe to newsletter', true)
            ],
            [title('Contact Information'), submit_label('Submit Data')]
        ).

    % Test retrieving form data
    test_form_data :-
        (   jupyter_forms::get_form_data(test_form, Data) ->
            write('Form data: '), write(Data), nl
        ;   write('No form data available'), nl
        ).

    % Test creating a form with generic input fields
    test_generic_input_form :-
        jupyter_forms::create_input_form(
            generic_form,
            [
                text_field(name, 'Name:', '<PERSON>'),
                input_field(phone, 'Phone:', [type-tel, placeholder-'******-123-4567']),
                input_field(website, 'Website:', [type-url, placeholder-'https://example.com']),
                input_field(birth_date, 'Birth Date:', [type-date]),
                input_field(favorite_color, 'Favorite Color:', [type-color, value-'#ff0000']),
                dropdown_field(category, 'Category:', [personal, business, other]),
                checkbox_field(notifications, 'Enable notifications', false)
            ],
            [title('Generic Input Form'), submit_label('Save')]
        ).

    % Test inheritance structure
    test_inheritance :-
        write('Testing inheritance structure:'), nl,
        % Test that jupyter_inputs is the base class
        (   jupyter_inputs::set_webserver('127.0.0.1', 8900) ->
            write('✓ jupyter_inputs::set_webserver/2 works'), nl
        ;   write('✗ jupyter_inputs::set_webserver/2 failed'), nl
        ),
        % Test that forms inherit webserver functionality
        (   jupyter_forms::webserver(IP, Port) ->
            format('✓ jupyter_forms::webserver/2 returns: ~w:~w~n', [IP, Port])
        ;   write('✗ jupyter_forms::webserver/2 failed'), nl
        ),
        % Test that widgets inherit webserver functionality
        (   jupyter_widgets::webserver(IP2, Port2) ->
            format('✓ jupyter_widgets::webserver/2 returns: ~w:~w~n', [IP2, Port2])
        ;   write('✗ jupyter_widgets::webserver/2 failed'), nl
        ).

:- end_object.
