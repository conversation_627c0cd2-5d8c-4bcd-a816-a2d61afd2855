LICENSE
MANIFEST.in
README.md
pyproject.toml
setup.cfg
logtalk_jupyter_kernel.egg-info/PKG-INFO
logtalk_jupyter_kernel.egg-info/SOURCES.txt
logtalk_jupyter_kernel.egg-info/dependency_links.txt
logtalk_jupyter_kernel.egg-info/requires.txt
logtalk_jupyter_kernel.egg-info/top_level.txt
logtalk_kernel/__init__.py
logtalk_kernel/__main__.py
logtalk_kernel/install.py
logtalk_kernel/kernel.py
logtalk_kernel/logtalk_kernel_base_implementation.py
logtalk_kernel/logtalk_kernel_config.py
logtalk_kernel/sicstus_kernel_implementation.py
logtalk_kernel/swi_kernel_implementation.py
logtalk_kernel/kernelspec/kernel.js
logtalk_kernel/kernelspec/kernel.json
logtalk_kernel/logtalk_server/jupyter.lgt
logtalk_kernel/logtalk_server/jupyter_forms.lgt
logtalk_kernel/logtalk_server/jupyter_inputs.lgt
logtalk_kernel/logtalk_server/jupyter_jsonrpc.lgt
logtalk_kernel/logtalk_server/jupyter_logging.lgt
logtalk_kernel/logtalk_server/jupyter_preferences.lgt
logtalk_kernel/logtalk_server/jupyter_query_handling.lgt
logtalk_kernel/logtalk_server/jupyter_request_handling.lgt
logtalk_kernel/logtalk_server/jupyter_server.lgt
logtalk_kernel/logtalk_server/jupyter_term_handling.lgt
logtalk_kernel/logtalk_server/jupyter_variable_bindings.lgt
logtalk_kernel/logtalk_server/jupyter_widgets.lgt
logtalk_kernel/logtalk_server/loader.lgt
notebooks/JupyterKernelForLogtalkOverview.ipynb
notebooks/LogtalkTutorial.ipynb